const { ApolloServer } = require('apollo-server-express');
const { createTestClient } = require('apollo-server-testing');
const { applyMiddleware } = require('graphql-middleware');
const graphqlTools = require('@graphql-tools/schema');

const typeDefs = require('../../graphql/schema');
const resolvers = require('../../graphql/resolvers');
const permissions = require('../../graphql/permissions');

// 1. 构建带有权限中间件的 Schema
const schema = graphqlTools.makeExecutableSchema({ typeDefs, resolvers });
const schemaWithPermissions = applyMiddleware(schema, permissions);

/**
 * 2. 创建一个测试服务器的辅助函数
 * @param {object} context - 模拟的请求上下文，用于代表不同用户
 * @returns {object} a test client
 */
const createTestServer = (context = {}) => {
  const server = new ApolloServer({
    schema: schemaWithPermissions,
    context: () => ({
      // 模拟 app.js 中的认证中间件
      // isAuth, userId, userType 等会在这里被设置
      req: { ...context }
    })
  });
  return createTestClient(server);
};

// 3. 开始编写测试用例
describe('GraphQL Permissions - Access Control Tests', () => {
  // === 定义一些通用的查询，用于测试 ===
  const GET_RESTAURANTS_QUERY = `query { restaurants { _id } }`;
  const GET_PROFILE_QUERY = `query { profile { _id } }`;
  const GET_USERS_QUERY = `query { users { _id } }`;
  const GET_ORDERS_QUERY = `query($restaurantId: ID!) { orders(restaurantId: $restaurantId) { _id } }`;

  // 场景1: 未认证用户 (访客)
  describe('As an Unauthenticated User', () => {
    let client;

    beforeAll(() => {
      // 创建一个没有任何认证信息的客户端
      client = createTestServer({});
    });

    it('should be ALLOWED to access public queries', async () => {
      const { data, errors } = await client.query({ query: GET_RESTAURANTS_QUERY });
      expect(errors).toBeUndefined();
      expect(data).toBeDefined();
      expect(data.restaurants).toBeInstanceOf(Array);
    });

    it('should be DENIED from accessing customer-only queries', async () => {
      const { data, errors } = await client.query({ query: GET_PROFILE_QUERY });
      expect(data.profile).toBeNull();
      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('Not Authorised!');
    });

    it('should be DENIED from accessing admin-only queries', async () => {
      const { data, errors } = await client.query({ query: GET_USERS_QUERY });
      expect(data.users).toBeNull();
      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('Not Authorised!');
    });
  });

  // 场景2: 已认证的普通客户 (Customer)
  describe('As an Authenticated Customer', () => {
    let client;

    beforeAll(() => {
      // 模拟一个已登录的客户
      client = createTestServer({
        isAuth: true,
        userId: 'customer_user_id_123',
        userType: 'default' // 'default' 对应 USER_ROLES.CUSTOMER
      });
    });

    it('should be ALLOWED to access public queries', async () => {
      const { errors } = await client.query({ query: GET_RESTAURANTS_QUERY });
      expect(errors).toBeUndefined();
    });

    it('should be ALLOWED to access their own profile', async () => {
      // 注意：这个测试需要数据库中有对应的数据才能返回data，但对于权限测试，
      // 只要不返回 "Not Authorised!" 错误，就说明权限通过了。
      // 我们可以通过检查错误是否为 "Not Authorised!" 来判断。
      const { errors } = await client.query({ query: GET_PROFILE_QUERY });
      // 假设没有数据会返回一个不同的错误，或者errors为undefined
      const isAuthError = errors?.some(e => e.message.includes('Not Authorised!'));
      expect(isAuthError).toBeFalsy();
    });

    it('should be DENIED from accessing restaurant-specific queries', async () => {
      const { errors } = await client.query({
        query: GET_ORDERS_QUERY,
        variables: { restaurantId: 'any_restaurant_id' }
      });
      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('Not Authorised!');
    });
  });

  // 场景3: 已认证的餐厅用户 (Restaurant)
  describe('As an Authenticated Restaurant User', () => {
    let client;

    beforeAll(() => {
      // 模拟一个已登录的餐厅用户
      client = createTestServer({
        isAuth: true,
        userId: 'restaurant_user_id_456',
        restaurantId: 'owned_restaurant_id_789', // 模拟该用户拥有的餐厅ID
        userType: 'RESTAURANT'
      });
    });

    it('should be DENIED from accessing customer-only queries', async () => {
      const { errors } = await client.query({ query: GET_PROFILE_QUERY });
      expect(errors).toBeDefined();
      expect(errors[0].message).toContain('Not Authorised!');
    });

    it('should be ALLOWED to access orders for their own restaurant', async () => {
      // isResourceOwner 规则会检查上下文中的 restaurantId 是否与查询参数匹配
      // 这里的测试需要更复杂的mock，模拟数据库查找来验证所有权。
      // 简单起见，我们先验证它没有因为角色错误而被拒绝。
      const { errors } = await client.query({
        query: GET_ORDERS_QUERY,
        variables: { restaurantId: 'owned_restaurant_id_789' }
      });
      const isAuthError = errors?.some(e => e.message.includes('Not Authorised!'));
      expect(isAuthError).toBeFalsy();
    });
  });

  // 你可以继续为 Admin, Rider, WhatsApp Token 等角色添加更多的 `describe` 块。
});
